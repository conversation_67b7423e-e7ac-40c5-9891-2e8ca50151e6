<script setup lang="ts">
import { ref, onMounted, computed, h } from 'vue';
import { NCard, NButton, NSpace, NDataTable, NInput, NSelect, NModal, NForm, NFormItem, NRadioGroup, NRadio } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';

defineOptions({
  name: 'QuestionBank'
});

interface Question {
  id: number;
  title: string;
  type: 'single' | 'multiple' | 'judge' | 'fill';
  difficulty: 'easy' | 'medium' | 'hard';
  subject: string;
  options?: string[];
  answer: string;
  createTime: string;
}

const loading = ref(false);
const showModal = ref(false);
const editingQuestion = ref<Question | null>(null);
const searchKeyword = ref('');
const selectedSubject = ref('');
const selectedDifficulty = ref('');

// 模拟数据
const questions = ref<Question[]>([
  {
    id: 1,
    title: 'Vue 3 中的 Composition API 的主要优势是什么？',
    type: 'single',
    difficulty: 'medium',
    subject: '前端开发',
    options: ['更好的类型推断', '更灵活的逻辑复用', '更好的性能', '以上都是'],
    answer: '以上都是',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    title: 'JavaScript 是一种编译型语言',
    type: 'judge',
    difficulty: 'easy',
    subject: '前端开发',
    answer: '错误',
    createTime: '2024-01-14 14:20:00'
  },
  {
    id: 3,
    title: '请写出 JavaScript 中数组去重的方法',
    type: 'fill',
    difficulty: 'hard',
    subject: '前端开发',
    answer: '[...new Set(array)] 或 array.filter((item, index) => array.indexOf(item) === index)',
    createTime: '2024-01-13 09:15:00'
  }
]);

const subjects = ref(['前端开发', '后端开发', '数据库', '算法', '系统设计']);
const difficulties = ref([
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' }
]);

const questionTypes = ref([
  { label: '单选题', value: 'single' },
  { label: '多选题', value: 'multiple' },
  { label: '判断题', value: 'judge' },
  { label: '填空题', value: 'fill' }
]);

// 表格列定义
const columns: DataTableColumns<Question> = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '题目',
    key: 'title',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '类型',
    key: 'type',
    width: 100,
    render(row) {
      const typeMap = {
        single: '单选题',
        multiple: '多选题',
        judge: '判断题',
        fill: '填空题'
      };
      return typeMap[row.type];
    }
  },
  {
    title: '难度',
    key: 'difficulty',
    width: 100,
    render(row) {
      const difficultyMap = {
        easy: '简单',
        medium: '中等',
        hard: '困难'
      };
      return difficultyMap[row.difficulty];
    }
  },
  {
    title: '科目',
    key: 'subject',
    width: 120
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      return [
        h(NButton, {
          size: 'small',
          type: 'primary',
          style: { marginRight: '8px' },
          onClick: () => handleEdit(row)
        }, { default: () => '编辑' }),
        h(NButton, {
          size: 'small',
          type: 'error',
          onClick: () => handleDelete(row.id)
        }, { default: () => '删除' })
      ];
    }
  }
];

// 新增题目表单
const formData = ref<Partial<Question>>({
  title: '',
  type: 'single',
  difficulty: 'easy',
  subject: '',
  options: ['', '', '', ''],
  answer: ''
});

const formRules = {
  title: {
    required: true,
    message: '请输入题目',
    trigger: 'blur'
  },
  subject: {
    required: true,
    message: '请选择科目',
    trigger: 'change'
  },
  answer: {
    required: true,
    message: '请输入答案',
    trigger: 'blur'
  }
};

// 筛选后的题目列表
const filteredQuestions = computed(() => {
  return questions.value.filter(question => {
    const matchKeyword = !searchKeyword.value || 
      question.title.toLowerCase().includes(searchKeyword.value.toLowerCase());
    const matchSubject = !selectedSubject.value || question.subject === selectedSubject.value;
    const matchDifficulty = !selectedDifficulty.value || question.difficulty === selectedDifficulty.value;
    
    return matchKeyword && matchSubject && matchDifficulty;
  });
});

function handleAdd() {
  editingQuestion.value = null;
  formData.value = {
    title: '',
    type: 'single',
    difficulty: 'easy',
    subject: '',
    options: ['', '', '', ''],
    answer: ''
  };
  showModal.value = true;
}

function handleEdit(question: Question) {
  editingQuestion.value = question;
  formData.value = { ...question };
  if (!formData.value.options) {
    formData.value.options = ['', '', '', ''];
  }
  showModal.value = true;
}

function handleDelete(id: number) {
  const index = questions.value.findIndex(q => q.id === id);
  if (index > -1) {
    questions.value.splice(index, 1);
    window.$message?.success('删除成功');
  }
}

function handleSave() {
  if (!formData.value.title || !formData.value.subject || !formData.value.answer) {
    window.$message?.error('请填写完整信息');
    return;
  }

  if (editingQuestion.value) {
    // 编辑
    const index = questions.value.findIndex(q => q.id === editingQuestion.value!.id);
    if (index > -1) {
      questions.value[index] = { ...formData.value as Question };
      window.$message?.success('更新成功');
    }
  } else {
    // 新增
    const newQuestion: Question = {
      ...formData.value as Question,
      id: Math.max(...questions.value.map(q => q.id)) + 1,
      createTime: new Date().toLocaleString('zh-CN')
    };
    questions.value.unshift(newQuestion);
    window.$message?.success('添加成功');
  }
  
  showModal.value = false;
}

function resetFilters() {
  searchKeyword.value = '';
  selectedSubject.value = '';
  selectedDifficulty.value = '';
}

onMounted(() => {
  // 初始化数据
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="题库管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <!-- 搜索和筛选区域 -->
      <div class="mb-16px">
        <NSpace>
          <NInput
            v-model:value="searchKeyword"
            placeholder="搜索题目关键词"
            clearable
            style="width: 200px"
          />
          <NSelect
            v-model:value="selectedSubject"
            placeholder="选择科目"
            :options="subjects.map(s => ({ label: s, value: s }))"
            clearable
            style="width: 150px"
          />
          <NSelect
            v-model:value="selectedDifficulty"
            placeholder="选择难度"
            :options="difficulties"
            clearable
            style="width: 120px"
          />
          <NButton @click="resetFilters">重置</NButton>
          <NButton type="primary" @click="handleAdd">新增题目</NButton>
        </NSpace>
      </div>

      <!-- 数据表格 -->
      <NDataTable
        :columns="columns"
        :data="filteredQuestions"
        :loading="loading"
        :pagination="{ pageSize: 10 }"
        :bordered="false"
        size="small"
        class="sm:h-320px"
      />
    </NCard>

    <!-- 新增/编辑题目弹窗 -->
    <NModal v-model:show="showModal" preset="dialog" :title="editingQuestion ? '编辑题目' : '新增题目'">
      <NForm :model="formData" :rules="formRules" label-placement="left" label-width="80px">
        <NFormItem label="题目" path="title">
          <NInput v-model:value="formData.title" placeholder="请输入题目" />
        </NFormItem>
        
        <NFormItem label="类型" path="type">
          <NRadioGroup v-model:value="formData.type">
            <NRadio v-for="type in questionTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <NFormItem label="难度" path="difficulty">
          <NSelect v-model:value="formData.difficulty" :options="difficulties" />
        </NFormItem>
        
        <NFormItem label="科目" path="subject">
          <NSelect
            v-model:value="formData.subject"
            :options="subjects.map(s => ({ label: s, value: s }))"
            placeholder="请选择科目"
          />
        </NFormItem>
        
        <!-- 选择题选项 -->
        <template v-if="formData.type === 'single' || formData.type === 'multiple'">
          <NFormItem label="选项A">
            <NInput v-model:value="formData.options![0]" placeholder="请输入选项A" />
          </NFormItem>
          <NFormItem label="选项B">
            <NInput v-model:value="formData.options![1]" placeholder="请输入选项B" />
          </NFormItem>
          <NFormItem label="选项C">
            <NInput v-model:value="formData.options![2]" placeholder="请输入选项C" />
          </NFormItem>
          <NFormItem label="选项D">
            <NInput v-model:value="formData.options![3]" placeholder="请输入选项D" />
          </NFormItem>
        </template>
        
        <NFormItem label="答案" path="answer">
          <NInput
            v-if="formData.type !== 'judge'"
            v-model:value="formData.answer"
            type="textarea"
            placeholder="请输入正确答案"
          />
          <NRadioGroup v-else v-model:value="formData.answer">
            <NRadio value="正确">正确</NRadio>
            <NRadio value="错误">错误</NRadio>
          </NRadioGroup>
        </NFormItem>
      </NForm>
      
      <template #action>
        <NSpace>
          <NButton @click="showModal = false">取消</NButton>
          <NButton type="primary" @click="handleSave">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
.card-wrapper {
  height: 100%;
}
</style>

<script setup lang="ts">
import { ref, onMounted, computed, h } from 'vue';
import { NCard, NButton, NSpace, NDataTable, NInput, NSelect, NModal, NForm, NFormItem, NRadioGroup, NRadio, NImage, NTag, NTooltip, NUpload, NUploadDragger, NIcon, NText } from 'naive-ui';
import type { DataTableColumns, UploadFileInfo } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'QuestionBank'
});

interface Question {
  id: number;
  title: string;
  type: 'single' | 'multiple' | 'judge' | 'fill';
  difficulty: 'easy' | 'medium' | 'hard';
  subject: string;
  cache_key_hash: string;
  image_url?: string;
  user_image?: string;
  options?: string[];
  answer: string;
  is_verified: boolean;
  createTime: string;
}

const loading = ref(false);
const showModal = ref(false);
const showImageModal = ref(false);
const previewImageUrl = ref('');
const editingQuestion = ref<Question | null>(null);
const searchKeyword = ref('');
const selectedSubject = ref('');
const selectedDifficulty = ref('');
const selectedVerifyStatus = ref('');

// 模拟数据
const questions = ref<Question[]>([
  {
    id: 1,
    title: 'Vue 3 中的 Composition API 的主要优势是什么？',
    type: 'single',
    difficulty: 'medium',
    subject: '前端开发',
    cache_key_hash: 'abc123def456',
    image_url: 'https://picsum.photos/400/300?random=1',
    user_image: 'https://picsum.photos/100/100?random=1',
    options: ['更好的类型推断', '更灵活的逻辑复用', '更好的性能', '以上都是'],
    answer: '以上都是',
    is_verified: true,
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    title: 'JavaScript 是一种编译型语言',
    type: 'judge',
    difficulty: 'easy',
    subject: '前端开发',
    cache_key_hash: 'xyz789uvw012',
    user_image: 'https://picsum.photos/100/100?random=2',
    answer: '错误',
    is_verified: false,
    createTime: '2024-01-14 14:20:00'
  },
  {
    id: 3,
    title: '请写出 JavaScript 中数组去重的方法',
    type: 'fill',
    difficulty: 'hard',
    subject: '前端开发',
    cache_key_hash: 'mno345pqr678',
    image_url: 'https://picsum.photos/400/300?random=3',
    answer: '[...new Set(array)] 或 array.filter((item, index) => array.indexOf(item) === index)',
    is_verified: true,
    createTime: '2024-01-13 09:15:00'
  }
]);

const subjects = ref(['前端开发', '后端开发', '数据库', '算法', '系统设计']);
const difficulties = ref([
  { label: '简单', value: 'easy' },
  { label: '中等', value: 'medium' },
  { label: '困难', value: 'hard' }
]);

const questionTypes = ref([
  { label: '单选题', value: 'single' },
  { label: '多选题', value: 'multiple' },
  { label: '判断题', value: 'judge' },
  { label: '填空题', value: 'fill' }
]);

const verifyStatusOptions = ref([
  { label: '已验证', value: true },
  { label: '未验证', value: false }
]);

// 表格列定义
const columns: DataTableColumns<Question> = [
  {
    title: 'ID',
    key: 'id',
    width: 60,
    fixed: 'left'
  },
  {
    title: '题目',
    key: 'title',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '哈希',
    key: 'cache_key_hash',
    width: 120,
    render(row) {
      return h(NTooltip, {
        trigger: 'hover'
      }, {
        trigger: () => h(NText, {
          style: {
            fontFamily: 'monospace',
            fontSize: '12px'
          }
        }, row.cache_key_hash.substring(0, 8) + '...'),
        default: () => row.cache_key_hash
      });
    }
  },
  {
    title: '题干图片',
    key: 'image_url',
    width: 100,
    render(row) {
      if (!row.image_url) return '无';
      return h(NImage, {
        width: 60,
        height: 40,
        src: row.image_url,
        previewSrc: row.image_url,
        objectFit: 'cover',
        style: { cursor: 'pointer', borderRadius: '4px' }
      });
    }
  },
  {
    title: '用户图片',
    key: 'user_image',
    width: 100,
    render(row) {
      if (!row.user_image) return '无';
      return h(NImage, {
        width: 40,
        height: 40,
        src: row.user_image,
        previewSrc: row.user_image,
        objectFit: 'cover',
        style: { cursor: 'pointer', borderRadius: '50%' }
      });
    }
  },
  {
    title: '类型',
    key: 'type',
    width: 80,
    render(row) {
      const typeMap = {
        single: { text: '单选', color: 'info' },
        multiple: { text: '多选', color: 'warning' },
        judge: { text: '判断', color: 'success' },
        fill: { text: '填空', color: 'error' }
      };
      const config = typeMap[row.type];
      return h(NTag, {
        type: config.color as any,
        size: 'small'
      }, { default: () => config.text });
    }
  },
  {
    title: '难度',
    key: 'difficulty',
    width: 80,
    render(row) {
      const difficultyMap = {
        easy: { text: '简单', color: 'success' },
        medium: { text: '中等', color: 'warning' },
        hard: { text: '困难', color: 'error' }
      };
      const config = difficultyMap[row.difficulty];
      return h(NTag, {
        type: config.color as any,
        size: 'small'
      }, { default: () => config.text });
    }
  },
  {
    title: '科目',
    key: 'subject',
    width: 100,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '验证状态',
    key: 'is_verified',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.is_verified ? 'success' : 'default',
        size: 'small'
      }, { default: () => row.is_verified ? '已验证' : '未验证' });
    }
  },
  {
    title: '选项',
    key: 'options',
    width: 150,
    render(row) {
      if (!row.options || row.options.length === 0) return '无';
      const optionsText = row.options.join(' | ');
      return h(NTooltip, {
        trigger: 'hover'
      }, {
        trigger: () => h(NText, {
          style: { fontSize: '12px' }
        }, optionsText.length > 20 ? optionsText.substring(0, 20) + '...' : optionsText),
        default: () => h('div', {
          style: { maxWidth: '300px', whiteSpace: 'pre-wrap' }
        }, row.options.map((opt, idx) => `${String.fromCharCode(65 + idx)}. ${opt}`).join('\n'))
      });
    }
  },
  {
    title: '答案',
    key: 'answer',
    width: 120,
    render(row) {
      return h(NTooltip, {
        trigger: 'hover'
      }, {
        trigger: () => h(NText, {
          style: { fontSize: '12px' }
        }, row.answer.length > 15 ? row.answer.substring(0, 15) + '...' : row.answer),
        default: () => h('div', {
          style: { maxWidth: '300px', whiteSpace: 'pre-wrap' }
        }, row.answer)
      });
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 150,
    render(row) {
      return h('div', {
        style: { fontSize: '12px' }
      }, row.createTime);
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right',
    render(row) {
      return h('div', {
        style: { display: 'flex', flexDirection: 'column', gap: '4px' }
      }, [
        h(NButton, {
          size: 'tiny',
          type: 'primary',
          onClick: () => handleEdit(row)
        }, { default: () => '编辑' }),
        h(NButton, {
          size: 'tiny',
          type: 'error',
          onClick: () => handleDelete(row.id)
        }, { default: () => '删除' })
      ]);
    }
  }
];

// 新增题目表单
const formData = ref<Partial<Question>>({
  title: '',
  type: 'single',
  difficulty: 'easy',
  subject: '',
  cache_key_hash: '',
  image_url: '',
  user_image: '',
  options: ['', '', '', ''],
  answer: '',
  is_verified: false
});

const formRules = {
  title: {
    required: true,
    message: '请输入题目',
    trigger: 'blur'
  },
  subject: {
    required: true,
    message: '请选择科目',
    trigger: 'change'
  },
  cache_key_hash: {
    required: true,
    message: '请输入哈希值',
    trigger: 'blur'
  },
  answer: {
    required: true,
    message: '请输入答案',
    trigger: 'blur'
  }
};

// 筛选后的题目列表
const filteredQuestions = computed(() => {
  return questions.value.filter(question => {
    const matchKeyword = !searchKeyword.value ||
      question.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      question.cache_key_hash.toLowerCase().includes(searchKeyword.value.toLowerCase());
    const matchSubject = !selectedSubject.value || question.subject === selectedSubject.value;
    const matchDifficulty = !selectedDifficulty.value || question.difficulty === selectedDifficulty.value;
    const matchVerifyStatus = selectedVerifyStatus.value === '' || question.is_verified === selectedVerifyStatus.value;

    return matchKeyword && matchSubject && matchDifficulty && matchVerifyStatus;
  });
});

function handleAdd() {
  editingQuestion.value = null;
  formData.value = {
    title: '',
    type: 'single',
    difficulty: 'easy',
    subject: '',
    cache_key_hash: '',
    image_url: '',
    user_image: '',
    options: ['', '', '', ''],
    answer: '',
    is_verified: false
  };
  showModal.value = true;
}

function handleEdit(question: Question) {
  editingQuestion.value = question;
  formData.value = { ...question };
  if (!formData.value.options) {
    formData.value.options = ['', '', '', ''];
  }
  showModal.value = true;
}

function handleDelete(id: number) {
  const index = questions.value.findIndex(q => q.id === id);
  if (index > -1) {
    questions.value.splice(index, 1);
    window.$message?.success('删除成功');
  }
}

function handleSave() {
  if (!formData.value.title || !formData.value.subject || !formData.value.answer || !formData.value.cache_key_hash) {
    window.$message?.error('请填写完整信息');
    return;
  }

  if (editingQuestion.value) {
    // 编辑
    const index = questions.value.findIndex(q => q.id === editingQuestion.value!.id);
    if (index > -1) {
      questions.value[index] = { ...formData.value as Question };
      window.$message?.success('更新成功');
    }
  } else {
    // 新增
    const newQuestion: Question = {
      ...formData.value as Question,
      id: Math.max(...questions.value.map(q => q.id)) + 1,
      createTime: new Date().toLocaleString('zh-CN')
    };
    questions.value.unshift(newQuestion);
    window.$message?.success('添加成功');
  }

  showModal.value = false;
}

function resetFilters() {
  searchKeyword.value = '';
  selectedSubject.value = '';
  selectedDifficulty.value = '';
  selectedVerifyStatus.value = '';
}

// 图片上传处理
function handleImageUpload(type: 'image_url' | 'user_image') {
  return (options: { file: UploadFileInfo }) => {
    // 这里应该上传到服务器，现在模拟返回URL
    const mockUrl = `https://picsum.photos/${type === 'user_image' ? '100/100' : '400/300'}?random=${Date.now()}`;
    formData.value[type] = mockUrl;
    window.$message?.success('图片上传成功');
    return Promise.resolve();
  };
}

onMounted(() => {
  // 初始化数据
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="题库管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <!-- 搜索和筛选区域 -->
      <div class="mb-16px">
        <NSpace wrap>
          <NInput
            v-model:value="searchKeyword"
            placeholder="搜索题目/哈希"
            clearable
            style="width: 180px"
          />
          <NSelect
            v-model:value="selectedSubject"
            placeholder="选择科目"
            :options="subjects.map(s => ({ label: s, value: s }))"
            clearable
            style="width: 120px"
          />
          <NSelect
            v-model:value="selectedDifficulty"
            placeholder="选择难度"
            :options="difficulties"
            clearable
            style="width: 100px"
          />
          <NSelect
            v-model:value="selectedVerifyStatus"
            placeholder="验证状态"
            :options="verifyStatusOptions"
            clearable
            style="width: 100px"
          />
          <NButton @click="resetFilters">重置</NButton>
          <NButton type="primary" @click="handleAdd">新增题目</NButton>
        </NSpace>
      </div>

      <!-- 数据表格 -->
      <NDataTable
        :columns="columns"
        :data="filteredQuestions"
        :loading="loading"
        :pagination="{ pageSize: 8 }"
        :bordered="false"
        size="small"
        :scroll-x="1800"
        class="sm:h-400px"
      />
    </NCard>

    <!-- 新增/编辑题目弹窗 -->
    <NModal
      v-model:show="showModal"
      preset="dialog"
      :title="editingQuestion ? '编辑题目' : '新增题目'"
      style="width: 800px"
    >
      <div style="max-height: 600px; overflow-y: auto;">
        <NForm :model="formData" :rules="formRules" label-placement="top">
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
            <NFormItem label="题目" path="title">
              <NInput v-model:value="formData.title" placeholder="请输入题目" type="textarea" :rows="3" />
            </NFormItem>

            <NFormItem label="哈希值" path="cache_key_hash">
              <NInput v-model:value="formData.cache_key_hash" placeholder="请输入哈希值" />
            </NFormItem>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px;">
            <NFormItem label="类型" path="type">
              <NSelect v-model:value="formData.type" :options="questionTypes" />
            </NFormItem>

            <NFormItem label="难度" path="difficulty">
              <NSelect v-model:value="formData.difficulty" :options="difficulties" />
            </NFormItem>

            <NFormItem label="科目" path="subject">
              <NSelect
                v-model:value="formData.subject"
                :options="subjects.map(s => ({ label: s, value: s }))"
                placeholder="请选择科目"
              />
            </NFormItem>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
            <NFormItem label="题干图片">
              <div>
                <NUpload
                  :custom-request="handleImageUpload('image_url')"
                  :show-file-list="false"
                  accept="image/*"
                >
                  <NUploadDragger style="height: 100px;">
                    <div style="margin-bottom: 12px;">
                      <SvgIcon icon="mdi:cloud-upload" style="font-size: 48px; color: #999;" />
                    </div>
                    <NText style="font-size: 14px;">
                      点击或者拖动文件到该区域来上传题干图片
                    </NText>
                  </NUploadDragger>
                </NUpload>
                <div v-if="formData.image_url" style="margin-top: 8px;">
                  <NImage
                    :src="formData.image_url"
                    width="100"
                    height="60"
                    object-fit="cover"
                    style="border-radius: 4px;"
                  />
                  <NButton
                    size="tiny"
                    type="error"
                    style="margin-left: 8px;"
                    @click="formData.image_url = ''"
                  >
                    删除
                  </NButton>
                </div>
              </div>
            </NFormItem>

            <NFormItem label="用户图片">
              <div>
                <NUpload
                  :custom-request="handleImageUpload('user_image')"
                  :show-file-list="false"
                  accept="image/*"
                >
                  <NUploadDragger style="height: 100px;">
                    <div style="margin-bottom: 12px;">
                      <SvgIcon icon="mdi:cloud-upload" style="font-size: 48px; color: #999;" />
                    </div>
                    <NText style="font-size: 14px;">
                      点击或者拖动文件到该区域来上传用户图片
                    </NText>
                  </NUploadDragger>
                </NUpload>
                <div v-if="formData.user_image" style="margin-top: 8px;">
                  <NImage
                    :src="formData.user_image"
                    width="60"
                    height="60"
                    object-fit="cover"
                    style="border-radius: 50%;"
                  />
                  <NButton
                    size="tiny"
                    type="error"
                    style="margin-left: 8px;"
                    @click="formData.user_image = ''"
                  >
                    删除
                  </NButton>
                </div>
              </div>
            </NFormItem>
          </div>

          <NFormItem label="验证状态">
            <NRadioGroup v-model:value="formData.is_verified">
              <NRadio :value="true">已验证</NRadio>
              <NRadio :value="false">未验证</NRadio>
            </NRadioGroup>
          </NFormItem>

          <!-- 选择题选项 -->
          <template v-if="formData.type === 'single' || formData.type === 'multiple'">
            <NFormItem label="选项">
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; width: 100%;">
                <NInput v-model:value="formData.options![0]" placeholder="选项A" />
                <NInput v-model:value="formData.options![1]" placeholder="选项B" />
                <NInput v-model:value="formData.options![2]" placeholder="选项C" />
                <NInput v-model:value="formData.options![3]" placeholder="选项D" />
              </div>
            </NFormItem>
          </template>

          <NFormItem label="答案" path="answer">
            <NInput
              v-if="formData.type !== 'judge'"
              v-model:value="formData.answer"
              type="textarea"
              :rows="3"
              placeholder="请输入正确答案"
            />
            <NRadioGroup v-else v-model:value="formData.answer">
              <NRadio value="正确">正确</NRadio>
              <NRadio value="错误">错误</NRadio>
            </NRadioGroup>
          </NFormItem>
        </NForm>
      </div>
      
      <template #action>
        <NSpace>
          <NButton @click="showModal = false">取消</NButton>
          <NButton type="primary" @click="handleSave">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
.card-wrapper {
  height: 100%;
}
</style>
